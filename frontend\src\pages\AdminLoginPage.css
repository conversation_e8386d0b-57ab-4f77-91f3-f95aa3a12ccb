/* Root Variables */
:root {
    --primary-color: #2C5F2D;
    --primary-hover: #224924;
    --secondary-color: #13141B;
    --accent-color: #00ff87;
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --error-color: #dc2626;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
}

/* Main Container */
.admin-login-container {
    display: flex;
    height: 100vh;
    width: 100%;
    overflow: hidden;
}

/* Left Panel - Login Form */
.login-left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 2rem 3rem;
    background-color: var(--white);
    overflow-y: auto;
}

/* Logo Section */
.login-logo {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    padding-left: 1rem;
}

.logo-icon {
    width: 24px;
    height: 24px;
    margin-right: 0.75rem;
    color: var(--primary-color);
    flex-shrink: 0;
}

.login-logo h1 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

/* Form Container */
.login-form-container {
    max-width: 360px;
    margin: auto;
    padding: 0;
}

.login-form-container h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--gray-500);
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

/* Security Notice */
.security-notice {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: rgba(44, 95, 45, 0.1);
    border: 1px solid rgba(44, 95, 45, 0.2);
    border-radius: var(--radius-md);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    color: var(--primary-color);
}

.security-icon {
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    flex-shrink: 0;
    color: var(--primary-color);
}

.security-icon svg {
    width: 100%;
    height: 100%;
}

/* Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

.input-container {
    position: relative;
    width: 100%;
}

.input-container input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 2.5rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--white);
}

.input-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 95, 45, 0.1);
}

.input-container input::placeholder {
    color: var(--gray-400);
}

.input-icon {
    position: absolute;
    top: 50%;
    right: 1rem;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: var(--gray-500);
    pointer-events: none;
}

.input-icon svg {
    width: 100%;
    height: 100%;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: nowrap;
    width: 100%;
}

.remember-me {
    display: flex;
    align-items: center;
    min-width: auto;
    flex-shrink: 0;
}

.remember-me input[type="checkbox"] {
    margin-right: 0.5rem;
    accent-color: var(--primary-color);
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.remember-me label {
    color: var(--gray-600);
    font-size: 0.875rem;
    white-space: nowrap;
    cursor: pointer;
}

.forgot-password {
    color: var(--primary-color);
    font-size: 0.875rem;
    text-decoration: none;
    transition: color 0.3s ease;
    margin-left: 1rem;
    white-space: nowrap;
}

.forgot-password:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Login Button */
.login-button {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.login-button:hover {
    background-color: var(--primary-hover);
}

.login-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Error Message */
.error-message {
    padding: 0.75rem;
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--error-color);
    border-radius: var(--radius-md);
    margin-bottom: 1.5rem;
    text-align: center;
    font-size: 0.875rem;
}

/* Right Panel - Login Image */
.login-right-panel {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-image: url('https://picsum.photos/1200/900');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.login-image {
    width: 100%;
    height: 100%;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .admin-login-container {
        flex-direction: column;
    }

    .login-left-panel {
        flex: none;
        width: 100%;
        padding: 1.5rem;
    }

    .login-right-panel {
        display: none;
    }

    .login-form-container {
        padding: 1rem 0;
    }
}

@media (max-width: 480px) {
    .login-logo h1 {
        font-size: 1.25rem;
    }

    .login-form-container h2 {
        font-size: 1.5rem;
    }

    .form-options {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
        width: 100%;
    }

    .remember-me {
        flex-shrink: 0;
    }

    .forgot-password {
        margin-left: auto;
    }
}
