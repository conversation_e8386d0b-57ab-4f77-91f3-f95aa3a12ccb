<?php
/**
 * Verify Admin OTP
 * Verifies the OTP code entered by admin and completes authentication
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['admin_id']) || !isset($input['otp'])) {
        throw new Exception("Admin ID and OTP are required");
    }
    
    $adminId = $input['admin_id'];
    $otpCode = $input['otp'];
    
    // Verify admin exists
    $stmt = $conn->prepare("SELECT admin_id, username, email, role FROM admins WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Admin not found");
    }
    
    // Get OTP settings
    $stmt = $conn->query("SELECT setting_name, setting_value FROM admin_auth_settings WHERE setting_name IN ('admin_max_otp_attempts', 'admin_lockout_time')");
    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_name']] = $row['setting_value'];
    }
    
    $maxAttempts = $settings['admin_max_otp_attempts'] ?? 3;
    $lockoutTime = $settings['admin_lockout_time'] ?? 1800; // 30 minutes default
    
    // Check if admin is locked out
    $stmt = $conn->prepare("SELECT locked_until FROM admin_otp WHERE admin_id = ? AND locked_until > NOW() ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$adminId]);
    $lockout = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($lockout) {
        throw new Exception("Account is temporarily locked due to too many failed attempts. Please try again later.");
    }
    
    // Get the most recent active OTP
    $stmt = $conn->prepare("
        SELECT id, otp, expires_at, attempts, used 
        FROM admin_otp 
        WHERE admin_id = ? AND expires_at > NOW() AND used = 0 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$adminId]);
    $otpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$otpRecord) {
        // Log failed attempt
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, 'otp', 'login_failed', ?, ?, ?)
        ");
        $stmt->execute([
            $adminId,
            json_encode(['reason' => 'otp_expired_or_not_found']),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        throw new Exception("OTP has expired or not found. Please request a new OTP.");
    }
    
    $conn->beginTransaction();
    
    // Check if OTP matches
    if ($otpRecord['otp'] !== $otpCode) {
        // Increment attempts
        $newAttempts = $otpRecord['attempts'] + 1;
        
        if ($newAttempts >= $maxAttempts) {
            // Lock the account
            $lockedUntil = date('Y-m-d H:i:s', time() + $lockoutTime);
            
            $stmt = $conn->prepare("
                UPDATE admin_otp 
                SET attempts = ?, locked_until = ? 
                WHERE id = ?
            ");
            $stmt->execute([$newAttempts, $lockedUntil, $otpRecord['id']]);
            
            // Log lockout
            $stmt = $conn->prepare("
                INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
                VALUES (?, 'otp', 'account_locked', ?, ?, ?)
            ");
            $stmt->execute([
                $adminId,
                json_encode(['reason' => 'max_otp_attempts_exceeded', 'locked_until' => $lockedUntil]),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            $conn->commit();
            
            throw new Exception("Too many failed OTP attempts. Account locked for " . ($lockoutTime / 60) . " minutes.");
        } else {
            // Update attempts count
            $stmt = $conn->prepare("UPDATE admin_otp SET attempts = ? WHERE id = ?");
            $stmt->execute([$newAttempts, $otpRecord['id']]);
            
            // Log failed attempt
            $stmt = $conn->prepare("
                INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
                VALUES (?, 'otp', 'login_failed', ?, ?, ?)
            ");
            $stmt->execute([
                $adminId,
                json_encode(['reason' => 'invalid_otp', 'attempts' => $newAttempts, 'max_attempts' => $maxAttempts]),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            $conn->commit();
            
            $remainingAttempts = $maxAttempts - $newAttempts;
            throw new Exception("Invalid OTP. You have $remainingAttempts attempt(s) remaining.");
        }
    }
    
    // OTP is valid - mark as used
    $stmt = $conn->prepare("UPDATE admin_otp SET used = 1 WHERE id = ?");
    $stmt->execute([$otpRecord['id']]);
    
    // Reset failed login attempts
    $stmt = $conn->prepare("UPDATE admins SET failed_login_attempts = 0, account_locked_until = NULL WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    
    // Update last login
    $stmt = $conn->prepare("UPDATE admins SET last_login = NOW() WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    
    // Log successful OTP verification
    $stmt = $conn->prepare("
        INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, 'otp', 'otp_verified', ?, ?, ?)
    ");
    $stmt->execute([
        $adminId,
        json_encode(['login_successful' => true]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    // Log successful login
    $stmt = $conn->prepare("
        INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, 'otp', 'login_success', ?, ?, ?)
    ");
    $stmt->execute([
        $adminId,
        json_encode(['auth_method' => 'otp']),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    $conn->commit();
    
    // Generate session token (you might want to implement JWT or similar)
    $sessionToken = bin2hex(random_bytes(32));
    
    echo json_encode([
        'success' => true,
        'message' => 'OTP verified successfully',
        'admin_id' => $admin['admin_id'],
        'username' => $admin['username'],
        'role' => $admin['role'],
        'session_token' => $sessionToken,
        'auth_method' => 'otp',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
