[{"C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js": "1", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js": "2", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js": "3", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js": "4", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js": "5", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js": "6", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js": "7", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js": "8", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js": "9", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js": "10", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js": "11", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js": "12", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js": "13", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js": "14", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js": "15", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js": "16", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js": "17", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js": "18", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js": "19", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js": "20", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js": "21", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js": "22", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js": "23", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js": "24", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js": "25", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js": "26", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js": "27", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js": "28", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js": "29", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js": "30", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js": "31", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js": "32", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js": "33", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js": "34", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js": "35", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js": "36", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js": "37", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js": "38", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js": "39", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js": "40", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js": "41", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js": "42", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js": "43", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js": "44", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js": "45", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js": "46", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js": "47", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js": "48", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js": "49", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js": "50", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js": "51", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js": "52", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js": "53", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js": "54", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js": "55", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js": "56", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js": "57", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js": "58", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js": "59", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js": "60", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js": "61", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js": "62", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js": "63", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js": "64", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js": "65", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js": "66", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js": "67", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js": "68", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js": "69", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js": "70", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js": "71", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js": "72", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js": "73", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js": "74", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js": "75", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js": "76", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js": "77", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js": "78", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js": "79", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js": "80", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js": "81", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js": "82", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js": "83", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js": "84", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js": "85", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js": "86", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js": "87"}, {"size": 1593, "mtime": 1739215325917, "results": "88", "hashOfConfig": "89"}, {"size": 11072, "mtime": 1749233340384, "results": "90", "hashOfConfig": "89"}, {"size": 362, "mtime": 1725527312699, "results": "91", "hashOfConfig": "89"}, {"size": 2707, "mtime": 1749107911322, "results": "92", "hashOfConfig": "89"}, {"size": 1583, "mtime": 1738380484748, "results": "93", "hashOfConfig": "89"}, {"size": 1958, "mtime": 1738907546846, "results": "94", "hashOfConfig": "89"}, {"size": 1431, "mtime": 1747472215947, "results": "95", "hashOfConfig": "89"}, {"size": 17038, "mtime": 1745585449129, "results": "96", "hashOfConfig": "89"}, {"size": 5850, "mtime": 1747430002225, "results": "97", "hashOfConfig": "89"}, {"size": 10378, "mtime": 1749231333045, "results": "98", "hashOfConfig": "89"}, {"size": 24430, "mtime": 1747478670935, "results": "99", "hashOfConfig": "89"}, {"size": 34669, "mtime": 1747471954224, "results": "100", "hashOfConfig": "89"}, {"size": 6588, "mtime": 1738406509501, "results": "101", "hashOfConfig": "89"}, {"size": 18314, "mtime": 1747665784063, "results": "102", "hashOfConfig": "89"}, {"size": 25409, "mtime": 1739175447004, "results": "103", "hashOfConfig": "89"}, {"size": 28140, "mtime": 1749118719068, "results": "104", "hashOfConfig": "89"}, {"size": 16128, "mtime": 1745598311818, "results": "105", "hashOfConfig": "89"}, {"size": 43308, "mtime": 1749118478833, "results": "106", "hashOfConfig": "89"}, {"size": 11677, "mtime": 1747481352521, "results": "107", "hashOfConfig": "89"}, {"size": 484, "mtime": 1747774257496, "results": "108", "hashOfConfig": "89"}, {"size": 3867, "mtime": 1749105548034, "results": "109", "hashOfConfig": "89"}, {"size": 13219, "mtime": 1747676927010, "results": "110", "hashOfConfig": "89"}, {"size": 14883, "mtime": 1738334800331, "results": "111", "hashOfConfig": "89"}, {"size": 6473, "mtime": 1747481379508, "results": "112", "hashOfConfig": "89"}, {"size": 398, "mtime": 1725625029363, "results": "113", "hashOfConfig": "89"}, {"size": 8347, "mtime": 1747666071848, "results": "114", "hashOfConfig": "89"}, {"size": 22489, "mtime": 1747647045462, "results": "115", "hashOfConfig": "89"}, {"size": 27005, "mtime": 1747687217258, "results": "116", "hashOfConfig": "89"}, {"size": 22327, "mtime": 1739035705052, "results": "117", "hashOfConfig": "89"}, {"size": 9112, "mtime": 1734251655762, "results": "118", "hashOfConfig": "89"}, {"size": 11975, "mtime": 1734248094632, "results": "119", "hashOfConfig": "89"}, {"size": 12979, "mtime": 1739086616323, "results": "120", "hashOfConfig": "89"}, {"size": 12071, "mtime": 1739001265026, "results": "121", "hashOfConfig": "89"}, {"size": 12037, "mtime": 1747670557722, "results": "122", "hashOfConfig": "89"}, {"size": 16681, "mtime": 1739089849375, "results": "123", "hashOfConfig": "89"}, {"size": 12891, "mtime": 1739078247803, "results": "124", "hashOfConfig": "89"}, {"size": 29344, "mtime": 1738182744405, "results": "125", "hashOfConfig": "89"}, {"size": 5324, "mtime": 1737964400994, "results": "126", "hashOfConfig": "89"}, {"size": 205, "mtime": 1732832805260, "results": "127", "hashOfConfig": "89"}, {"size": 28050, "mtime": 1738011980316, "results": "128", "hashOfConfig": "89"}, {"size": 30253, "mtime": 1737968307123, "results": "129", "hashOfConfig": "89"}, {"size": 8917, "mtime": 1738228976181, "results": "130", "hashOfConfig": "89"}, {"size": 1242, "mtime": 1732832820214, "results": "131", "hashOfConfig": "89"}, {"size": 1134, "mtime": 1732832829902, "results": "132", "hashOfConfig": "89"}, {"size": 1098, "mtime": 1732832839965, "results": "133", "hashOfConfig": "89"}, {"size": 11530, "mtime": 1732983571250, "results": "134", "hashOfConfig": "89"}, {"size": 23454, "mtime": 1738253936762, "results": "135", "hashOfConfig": "89"}, {"size": 24467, "mtime": 1732988420840, "results": "136", "hashOfConfig": "89"}, {"size": 4310, "mtime": 1734245942035, "results": "137", "hashOfConfig": "89"}, {"size": 5623, "mtime": 1734245958195, "results": "138", "hashOfConfig": "89"}, {"size": 3339, "mtime": 1734245925091, "results": "139", "hashOfConfig": "89"}, {"size": 6337, "mtime": 1736487062211, "results": "140", "hashOfConfig": "89"}, {"size": 5681, "mtime": 1734287339563, "results": "141", "hashOfConfig": "89"}, {"size": 10920, "mtime": 1739168463615, "results": "142", "hashOfConfig": "89"}, {"size": 14257, "mtime": 1739212427178, "results": "143", "hashOfConfig": "89"}, {"size": 16913, "mtime": 1738012431449, "results": "144", "hashOfConfig": "89"}, {"size": 21192, "mtime": 1738014939015, "results": "145", "hashOfConfig": "89"}, {"size": 3211, "mtime": 1747478622718, "results": "146", "hashOfConfig": "89"}, {"size": 4667, "mtime": 1749233297141, "results": "147", "hashOfConfig": "89"}, {"size": 1352, "mtime": 1738907631772, "results": "148", "hashOfConfig": "89"}, {"size": 591, "mtime": 1737714035353, "results": "149", "hashOfConfig": "89"}, {"size": 4889, "mtime": 1739089917990, "results": "150", "hashOfConfig": "89"}, {"size": 4026, "mtime": 1749114060143, "results": "151", "hashOfConfig": "89"}, {"size": 981, "mtime": 1747467742714, "results": "152", "hashOfConfig": "89"}, {"size": 597, "mtime": 1738005020143, "results": "153", "hashOfConfig": "89"}, {"size": 2649, "mtime": 1745558530865, "results": "154", "hashOfConfig": "89"}, {"size": 856, "mtime": 1738005002533, "results": "155", "hashOfConfig": "89"}, {"size": 778, "mtime": 1737703033090, "results": "156", "hashOfConfig": "89"}, {"size": 9268, "mtime": 1739089382382, "results": "157", "hashOfConfig": "89"}, {"size": 4473, "mtime": 1739114665777, "results": "158", "hashOfConfig": "89"}, {"size": 6511, "mtime": 1747772230646, "results": "159", "hashOfConfig": "89"}, {"size": 3561, "mtime": 1747465926259, "results": "160", "hashOfConfig": "89"}, {"size": 2058, "mtime": 1745560016985, "results": "161", "hashOfConfig": "89"}, {"size": 3270, "mtime": 1747683592095, "results": "162", "hashOfConfig": "89"}, {"size": 13040, "mtime": 1749112054896, "results": "163", "hashOfConfig": "89"}, {"size": 35666, "mtime": 1749231075399, "results": "164", "hashOfConfig": "89"}, {"size": 17158, "mtime": 1749113919875, "results": "165", "hashOfConfig": "89"}, {"size": 12332, "mtime": 1749106493490, "results": "166", "hashOfConfig": "89"}, {"size": 1631, "mtime": 1749111942679, "results": "167", "hashOfConfig": "89"}, {"size": 35110, "mtime": 1749118685592, "results": "168", "hashOfConfig": "89"}, {"size": 17861, "mtime": 1749116896225, "results": "169", "hashOfConfig": "89"}, {"size": 317, "mtime": 1749231241721, "results": "170", "hashOfConfig": "89"}, {"size": 14859, "mtime": 1749231165875, "results": "171", "hashOfConfig": "89"}, {"size": 10875, "mtime": 1749231195499, "results": "172", "hashOfConfig": "89"}, {"size": 10108, "mtime": 1749231124420, "results": "173", "hashOfConfig": "89"}, {"size": 12103, "mtime": 1749231233630, "results": "174", "hashOfConfig": "89"}, {"size": 17098, "mtime": 1749233272069, "results": "175", "hashOfConfig": "89"}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eamnk2", {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js", ["437"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js", ["438"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js", ["439", "440", "441"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js", ["442", "443", "444", "445", "446", "447", "448", "449", "450", "451"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js", ["452", "453"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js", ["454", "455", "456", "457", "458", "459", "460"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js", ["461"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js", ["462", "463"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js", ["464", "465", "466"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js", ["467"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js", ["468", "469"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js", ["470", "471", "472", "473"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js", ["474", "475", "476"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js", ["477", "478"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js", ["479"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js", ["480", "481", "482", "483", "484"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js", ["485"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js", ["486"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js", ["487", "488", "489", "490", "491"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js", ["492", "493", "494", "495", "496"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js", ["497"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js", ["498"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js", ["499"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js", ["500", "501"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js", ["502", "503", "504", "505"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js", ["506"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js", ["507", "508"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js", ["509"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js", ["510", "511", "512"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js", ["513"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js", ["514", "515", "516", "517"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js", ["518"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js", ["519", "520", "521", "522"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js", ["523", "524"], [], {"ruleId": "525", "severity": 1, "message": "526", "line": 67, "column": 8, "nodeType": "527", "messageId": "528", "endLine": 67, "endColumn": 23}, {"ruleId": "529", "severity": 1, "message": "530", "line": 31, "column": 6, "nodeType": "531", "endLine": 31, "endColumn": 8, "suggestions": "532"}, {"ruleId": "529", "severity": 1, "message": "533", "line": 25, "column": 9, "nodeType": "534", "endLine": 25, "endColumn": 62}, {"ruleId": "529", "severity": 1, "message": "535", "line": 73, "column": 6, "nodeType": "531", "endLine": 73, "endColumn": 50, "suggestions": "536"}, {"ruleId": "529", "severity": 1, "message": "537", "line": 119, "column": 6, "nodeType": "531", "endLine": 119, "endColumn": 85, "suggestions": "538"}, {"ruleId": "525", "severity": 1, "message": "539", "line": 8, "column": 3, "nodeType": "527", "messageId": "528", "endLine": 8, "endColumn": 16}, {"ruleId": "525", "severity": 1, "message": "540", "line": 10, "column": 3, "nodeType": "527", "messageId": "528", "endLine": 10, "endColumn": 17}, {"ruleId": "525", "severity": 1, "message": "541", "line": 11, "column": 3, "nodeType": "527", "messageId": "528", "endLine": 11, "endColumn": 13}, {"ruleId": "525", "severity": 1, "message": "542", "line": 12, "column": 3, "nodeType": "527", "messageId": "528", "endLine": 12, "endColumn": 16}, {"ruleId": "525", "severity": 1, "message": "543", "line": 13, "column": 3, "nodeType": "527", "messageId": "528", "endLine": 13, "endColumn": 8}, {"ruleId": "525", "severity": 1, "message": "544", "line": 14, "column": 3, "nodeType": "527", "messageId": "528", "endLine": 14, "endColumn": 16}, {"ruleId": "525", "severity": 1, "message": "545", "line": 15, "column": 3, "nodeType": "527", "messageId": "528", "endLine": 15, "endColumn": 10}, {"ruleId": "525", "severity": 1, "message": "546", "line": 16, "column": 3, "nodeType": "527", "messageId": "528", "endLine": 16, "endColumn": 9}, {"ruleId": "525", "severity": 1, "message": "547", "line": 20, "column": 16, "nodeType": "527", "messageId": "528", "endLine": 20, "endColumn": 19}, {"ruleId": "525", "severity": 1, "message": "548", "line": 47, "column": 12, "nodeType": "527", "messageId": "528", "endLine": 47, "endColumn": 30}, {"ruleId": "525", "severity": 1, "message": "549", "line": 20, "column": 10, "nodeType": "527", "messageId": "528", "endLine": 20, "endColumn": 20}, {"ruleId": "525", "severity": 1, "message": "550", "line": 20, "column": 22, "nodeType": "527", "messageId": "528", "endLine": 20, "endColumn": 35}, {"ruleId": "525", "severity": 1, "message": "551", "line": 3, "column": 20, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 28}, {"ruleId": "525", "severity": 1, "message": "552", "line": 3, "column": 37, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 43}, {"ruleId": "525", "severity": 1, "message": "553", "line": 3, "column": 45, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 52}, {"ruleId": "525", "severity": 1, "message": "554", "line": 13, "column": 21, "nodeType": "527", "messageId": "528", "endLine": 13, "endColumn": 31}, {"ruleId": "529", "severity": 1, "message": "555", "line": 40, "column": 8, "nodeType": "531", "endLine": 40, "endColumn": 41, "suggestions": "556"}, {"ruleId": "525", "severity": 1, "message": "557", "line": 111, "column": 11, "nodeType": "527", "messageId": "528", "endLine": 111, "endColumn": 27}, {"ruleId": "525", "severity": 1, "message": "558", "line": 144, "column": 11, "nodeType": "527", "messageId": "528", "endLine": 144, "endColumn": 22}, {"ruleId": "525", "severity": 1, "message": "559", "line": 17, "column": 12, "nodeType": "527", "messageId": "528", "endLine": 17, "endColumn": 21}, {"ruleId": "525", "severity": 1, "message": "551", "line": 3, "column": 20, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 28}, {"ruleId": "529", "severity": 1, "message": "560", "line": 39, "column": 8, "nodeType": "531", "endLine": 39, "endColumn": 42, "suggestions": "561"}, {"ruleId": "525", "severity": 1, "message": "562", "line": 3, "column": 10, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 22}, {"ruleId": "529", "severity": 1, "message": "563", "line": 24, "column": 8, "nodeType": "531", "endLine": 24, "endColumn": 19, "suggestions": "564"}, {"ruleId": "529", "severity": 1, "message": "565", "line": 33, "column": 8, "nodeType": "531", "endLine": 33, "endColumn": 30, "suggestions": "566"}, {"ruleId": "529", "severity": 1, "message": "567", "line": 43, "column": 8, "nodeType": "531", "endLine": 43, "endColumn": 10, "suggestions": "568"}, {"ruleId": "525", "severity": 1, "message": "569", "line": 17, "column": 5, "nodeType": "527", "messageId": "528", "endLine": 17, "endColumn": 17}, {"ruleId": "525", "severity": 1, "message": "570", "line": 18, "column": 5, "nodeType": "527", "messageId": "528", "endLine": 18, "endColumn": 12}, {"ruleId": "525", "severity": 1, "message": "571", "line": 1, "column": 60, "nodeType": "527", "messageId": "528", "endLine": 1, "endColumn": 66}, {"ruleId": "525", "severity": 1, "message": "572", "line": 28, "column": 12, "nodeType": "527", "messageId": "528", "endLine": 28, "endColumn": 26}, {"ruleId": "525", "severity": 1, "message": "573", "line": 28, "column": 28, "nodeType": "527", "messageId": "528", "endLine": 28, "endColumn": 45}, {"ruleId": "529", "severity": 1, "message": "574", "line": 83, "column": 8, "nodeType": "531", "endLine": 83, "endColumn": 10, "suggestions": "575"}, {"ruleId": "525", "severity": 1, "message": "576", "line": 3, "column": 19, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 30}, {"ruleId": "525", "severity": 1, "message": "577", "line": 14, "column": 12, "nodeType": "527", "messageId": "528", "endLine": 14, "endColumn": 24}, {"ruleId": "525", "severity": 1, "message": "578", "line": 93, "column": 11, "nodeType": "527", "messageId": "528", "endLine": 93, "endColumn": 23}, {"ruleId": "525", "severity": 1, "message": "579", "line": 114, "column": 9, "nodeType": "527", "messageId": "528", "endLine": 114, "endColumn": 25}, {"ruleId": "525", "severity": 1, "message": "580", "line": 148, "column": 9, "nodeType": "527", "messageId": "528", "endLine": 148, "endColumn": 22}, {"ruleId": "525", "severity": 1, "message": "581", "line": 4, "column": 10, "nodeType": "527", "messageId": "528", "endLine": 4, "endColumn": 17}, {"ruleId": "525", "severity": 1, "message": "582", "line": 13, "column": 10, "nodeType": "527", "messageId": "528", "endLine": 13, "endColumn": 17}, {"ruleId": "525", "severity": 1, "message": "583", "line": 14, "column": 10, "nodeType": "527", "messageId": "528", "endLine": 14, "endColumn": 15}, {"ruleId": "525", "severity": 1, "message": "580", "line": 115, "column": 9, "nodeType": "527", "messageId": "528", "endLine": 115, "endColumn": 22}, {"ruleId": "525", "severity": 1, "message": "584", "line": 132, "column": 9, "nodeType": "527", "messageId": "528", "endLine": 132, "endColumn": 19}, {"ruleId": "525", "severity": 1, "message": "585", "line": 145, "column": 9, "nodeType": "527", "messageId": "528", "endLine": 145, "endColumn": 22}, {"ruleId": "529", "severity": 1, "message": "560", "line": 44, "column": 8, "nodeType": "531", "endLine": 44, "endColumn": 21, "suggestions": "586"}, {"ruleId": "529", "severity": 1, "message": "587", "line": 19, "column": 8, "nodeType": "531", "endLine": 19, "endColumn": 10, "suggestions": "588"}, {"ruleId": "525", "severity": 1, "message": "589", "line": 6, "column": 14, "nodeType": "527", "messageId": "528", "endLine": 6, "endColumn": 20}, {"ruleId": "525", "severity": 1, "message": "569", "line": 6, "column": 41, "nodeType": "527", "messageId": "528", "endLine": 6, "endColumn": 53}, {"ruleId": "525", "severity": 1, "message": "590", "line": 7, "column": 46, "nodeType": "527", "messageId": "528", "endLine": 7, "endColumn": 52}, {"ruleId": "525", "severity": 1, "message": "591", "line": 11, "column": 7, "nodeType": "527", "messageId": "528", "endLine": 11, "endColumn": 19}, {"ruleId": "525", "severity": 1, "message": "592", "line": 307, "column": 11, "nodeType": "527", "messageId": "528", "endLine": 307, "endColumn": 27}, {"ruleId": "525", "severity": 1, "message": "593", "line": 4, "column": 45, "nodeType": "527", "messageId": "528", "endLine": 4, "endColumn": 64}, {"ruleId": "525", "severity": 1, "message": "594", "line": 4, "column": 66, "nodeType": "527", "messageId": "528", "endLine": 4, "endColumn": 79}, {"ruleId": "525", "severity": 1, "message": "595", "line": 4, "column": 111, "nodeType": "527", "messageId": "528", "endLine": 4, "endColumn": 123}, {"ruleId": "529", "severity": 1, "message": "596", "line": 29, "column": 8, "nodeType": "531", "endLine": 29, "endColumn": 10, "suggestions": "597"}, {"ruleId": "525", "severity": 1, "message": "598", "line": 256, "column": 11, "nodeType": "527", "messageId": "528", "endLine": 256, "endColumn": 26}, {"ruleId": "529", "severity": 1, "message": "599", "line": 24, "column": 8, "nodeType": "531", "endLine": 24, "endColumn": 33, "suggestions": "600"}, {"ruleId": "525", "severity": 1, "message": "589", "line": 5, "column": 57, "nodeType": "527", "messageId": "528", "endLine": 5, "endColumn": 63}, {"ruleId": "529", "severity": 1, "message": "601", "line": 24, "column": 8, "nodeType": "531", "endLine": 24, "endColumn": 33, "suggestions": "602"}, {"ruleId": "525", "severity": 1, "message": "603", "line": 122, "column": 19, "nodeType": "527", "messageId": "528", "endLine": 122, "endColumn": 28}, {"ruleId": "525", "severity": 1, "message": "604", "line": 137, "column": 19, "nodeType": "527", "messageId": "528", "endLine": 137, "endColumn": 22}, {"ruleId": "525", "severity": 1, "message": "605", "line": 4, "column": 5, "nodeType": "527", "messageId": "528", "endLine": 4, "endColumn": 14}, {"ruleId": "525", "severity": 1, "message": "606", "line": 6, "column": 5, "nodeType": "527", "messageId": "528", "endLine": 6, "endColumn": 10}, {"ruleId": "525", "severity": 1, "message": "541", "line": 7, "column": 5, "nodeType": "527", "messageId": "528", "endLine": 7, "endColumn": 15}, {"ruleId": "525", "severity": 1, "message": "607", "line": 8, "column": 5, "nodeType": "527", "messageId": "528", "endLine": 8, "endColumn": 16}, {"ruleId": "525", "severity": 1, "message": "608", "line": 57, "column": 9, "nodeType": "527", "messageId": "528", "endLine": 57, "endColumn": 26}, {"ruleId": "525", "severity": 1, "message": "540", "line": 2, "column": 10, "nodeType": "527", "messageId": "528", "endLine": 2, "endColumn": 24}, {"ruleId": "525", "severity": 1, "message": "609", "line": 2, "column": 26, "nodeType": "527", "messageId": "528", "endLine": 2, "endColumn": 34}, {"ruleId": "525", "severity": 1, "message": "610", "line": 3, "column": 35, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 41}, {"ruleId": "525", "severity": 1, "message": "611", "line": 5, "column": 5, "nodeType": "527", "messageId": "528", "endLine": 5, "endColumn": 15}, {"ruleId": "525", "severity": 1, "message": "539", "line": 6, "column": 5, "nodeType": "527", "messageId": "528", "endLine": 6, "endColumn": 18}, {"ruleId": "529", "severity": 1, "message": "612", "line": 29, "column": 8, "nodeType": "531", "endLine": 29, "endColumn": 31, "suggestions": "613"}, {"ruleId": "529", "severity": 1, "message": "560", "line": 31, "column": 8, "nodeType": "531", "endLine": 31, "endColumn": 17, "suggestions": "614"}, {"ruleId": "525", "severity": 1, "message": "615", "line": 3, "column": 23, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 31}, {"ruleId": "525", "severity": 1, "message": "616", "line": 3, "column": 33, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 38}, {"ruleId": "525", "severity": 1, "message": "617", "line": 14, "column": 12, "nodeType": "527", "messageId": "528", "endLine": 14, "endColumn": 21}, {"ruleId": "529", "severity": 1, "message": "618", "line": 29, "column": 8, "nodeType": "531", "endLine": 29, "endColumn": 10, "suggestions": "619"}, {"ruleId": "529", "severity": 1, "message": "620", "line": 22, "column": 8, "nodeType": "531", "endLine": 22, "endColumn": 10, "suggestions": "621"}, {"ruleId": "525", "severity": 1, "message": "570", "line": 3, "column": 51, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 58}, {"ruleId": "525", "severity": 1, "message": "606", "line": 3, "column": 102, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 107}, {"ruleId": "525", "severity": 1, "message": "622", "line": 18, "column": 12, "nodeType": "527", "messageId": "528", "endLine": 18, "endColumn": 21}, {"ruleId": "529", "severity": 1, "message": "623", "line": 23, "column": 8, "nodeType": "531", "endLine": 23, "endColumn": 17, "suggestions": "624"}, {"ruleId": "525", "severity": 1, "message": "625", "line": 3, "column": 40, "nodeType": "527", "messageId": "528", "endLine": 3, "endColumn": 46}, {"ruleId": "529", "severity": 1, "message": "626", "line": 30, "column": 8, "nodeType": "531", "endLine": 30, "endColumn": 10, "suggestions": "627"}, "no-unused-vars", "'LeagueSelection' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeError'. Either include it or remove the dependency array.", "ArrayExpression", ["628"], "The 'publicRoutes' array makes the dependencies of useCallback Hook (at line 97) change on every render. To fix this, wrap the initialization of 'publicRoutes' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'setUserData'. Either include it or remove the dependency array.", ["629"], "React Hook useEffect has a missing dependency: 'publicRoutes'. Either include it or remove the dependency array.", ["630"], "'FaCalendarAlt' is defined but never used.", "'FaFootballBall' is defined but never used.", "'FaChartBar' is defined but never used.", "'FaExchangeAlt' is defined but never used.", "'FaEye' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaClock' is defined but never used.", "'FaBell' is defined but never used.", "'Bar' is defined but never used.", "'teamPopularityData' is assigned a value but never used.", "'challenges' is assigned a value but never used.", "'setChallenges' is assigned a value but never used.", "'FaFilter' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'setSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllBets'. Either include it or remove the dependency array.", ["631"], "'handlePageChange' is assigned a value but never used.", "'getTeamLogo' is assigned a value but never used.", "'otpExpiry' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLeaderboard'. Either include it or remove the dependency array.", ["632"], "'API_BASE_URL' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchConversations'. Either include it or remove the dependency array.", ["633"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'markMessagesAsRead'. Either include them or remove the dependency array.", ["634"], "React Hook useEffect has a missing dependency: 'checkChallengeStatus'. Either include it or remove the dependency array.", ["635"], "'FaInfoCircle' is defined but never used.", "'FaTimes' is defined but never used.", "'useRef' is defined but never used.", "'payoutPreviews' is assigned a value but never used.", "'setPayoutPreviews' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'fetchLeagueDetails'. Either include it or remove the dependency array.", ["636"], "'FaChartLine' is defined but never used.", "'loadingUsers' is assigned a value but never used.", "'currentUsers' is assigned a value but never used.", "'renderPagination' is assigned a value but never used.", "'calculateOdds' is assigned a value but never used.", "'FaCoins' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", "'getUserStatus' is assigned a value but never used.", ["637"], "React Hook useEffect has a missing dependency: 'fetchPendingRequests'. Either include it or remove the dependency array.", ["638"], "'FaStar' is defined but never used.", "'FaFire' is defined but never used.", "'API_BASE_URL' is assigned a value but never used.", "'renderNavigation' is assigned a value but never used.", "'FaExclamationCircle' is defined but never used.", "'FaUserFriends' is defined but never used.", "'FaUserCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFriends'. Either include it or remove the dependency array.", ["639"], "'handleChallenge' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["640"], "React Hook useEffect has a missing dependency: 'fetchCreditRequests'. Either include it or remove the dependency array.", ["641"], "'matchDate' is assigned a value but never used.", "'now' is assigned a value but never used.", "'FaGamepad' is defined but never used.", "'FaCog' is defined but never used.", "'FaMoneyBill' is defined but never used.", "'handleLogoutClick' is assigned a value but never used.", "'FaTrophy' is defined but never used.", "'FaSave' is defined but never used.", "'FaDownload' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReport'. Either include it or remove the dependency array.", ["642"], ["643"], "'FaQrcode' is defined but never used.", "'FaKey' is defined but never used.", "'secretKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initiate2FASetup'. Either include it or remove the dependency array.", ["644"], "React Hook useEffect has a missing dependency: 'sendOTP'. Either include it or remove the dependency array.", ["645"], "'adminInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPreferences'. Either include it or remove the dependency array.", ["646"], "'FaCopy' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["647"], {"desc": "648", "fix": "649"}, {"desc": "650", "fix": "651"}, {"desc": "652", "fix": "653"}, {"desc": "654", "fix": "655"}, {"desc": "656", "fix": "657"}, {"desc": "658", "fix": "659"}, {"desc": "660", "fix": "661"}, {"desc": "662", "fix": "663"}, {"desc": "664", "fix": "665"}, {"desc": "666", "fix": "667"}, {"desc": "668", "fix": "669"}, {"desc": "670", "fix": "671"}, {"desc": "672", "fix": "673"}, {"desc": "674", "fix": "675"}, {"desc": "676", "fix": "677"}, {"desc": "678", "fix": "679"}, {"desc": "680", "fix": "681"}, {"desc": "682", "fix": "683"}, {"desc": "684", "fix": "685"}, {"desc": "686", "fix": "687"}, "Update the dependencies array to be: [removeError]", {"range": "688", "text": "689"}, "Update the dependencies array to be: [token, userId, setUserData, navigate, location.pathname]", {"range": "690", "text": "691"}, "Update the dependencies array to be: [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", {"range": "692", "text": "693"}, "Update the dependencies array to be: [pagination.currentPage, filters, fetchAllBets]", {"range": "694", "text": "695"}, "Update the dependencies array to be: [pagination.current_page, filters, fetchLeaderboard]", {"range": "696", "text": "697"}, "Update the dependencies array to be: [activeTab, fetchConversations]", {"range": "698", "text": "699"}, "Update the dependencies array to be: [fetchMessages, markMessagesAsRead, selectedConversation]", {"range": "700", "text": "701"}, "Update the dependencies array to be: [checkChallengeStatus]", {"range": "702", "text": "703"}, "Update the dependencies array to be: [fetchLeagueDetails]", {"range": "704", "text": "705"}, "Update the dependencies array to be: [currentPage, fetchLeaderboard]", {"range": "706", "text": "707"}, "Update the dependencies array to be: [fetchPendingRequests]", {"range": "708", "text": "709"}, "Update the dependencies array to be: [fetchFriends]", {"range": "710", "text": "711"}, "Update the dependencies array to be: [navigate, currentUserId, fetchInitialData]", {"range": "712", "text": "713"}, "Update the dependencies array to be: [navigate, currentUserId, fetchCreditRequests]", {"range": "714", "text": "715"}, "Update the dependencies array to be: [reportType, dateRange, fetchReport]", {"range": "716", "text": "717"}, "Update the dependencies array to be: [fetchLeaderboard, filters]", {"range": "718", "text": "719"}, "Update the dependencies array to be: [initiate2FASetup]", {"range": "720", "text": "721"}, "Update the dependencies array to be: [sendOTP]", {"range": "722", "text": "723"}, "Update the dependencies array to be: [adminId, fetchPreferences]", {"range": "724", "text": "725"}, "Update the dependencies array to be: [fetchAdminData]", {"range": "726", "text": "727"}, [985, 987], "[removeError]", [2571, 2615], "[token, userId, setUserData, navigate, location.pathname]", [4000, 4079], "[token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", [1105, 1138], "[pagination.currentPage, filters, fetchAllBets]", [1249, 1283], "[pagination.current_page, filters, fetchLeaderboard]", [1116, 1127], "[activeTab, fetchConversations]", [1474, 1496], "[fetch<PERSON>essages, markMessagesAsRead, selectedConversation]", [1532, 1534], "[checkChallengeStatus]", [3030, 3032], "[fetchLeagueDetails]", [1160, 1173], "[currentPage, fetchLeaderboard]", [696, 698], "[fetchPendingRequests]", [1331, 1333], "[fetchFriends]", [856, 881], "[navigate, currentUserId, fetchInitialData]", [891, 916], "[navigate, currentUserId, fetchCreditRequests]", [864, 887], "[reportType, dateRange, fetchReport]", [887, 896], "[fetchLeaderboard, filters]", [1137, 1139], "[initiate2FASetup]", [867, 869], "[sendOTP]", [856, 865], "[adminId, fetchPreferences]", [1269, 1271], "[fetchAdminData]"]