<?php
/**
 * Send OTP to Admin Email
 * Generates and sends OTP code to admin's registered email address
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';
require_once '../vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['admin_id'])) {
        throw new Exception("Admin ID required");
    }
    
    $adminId = $input['admin_id'];
    
    // Verify admin exists and get details
    $stmt = $conn->prepare("SELECT admin_id, username, email, auth_method FROM admins WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Admin not found");
    }
    
    // Check if OTP is enabled globally
    $stmt = $conn->prepare("SELECT setting_value FROM admin_auth_settings WHERE setting_name = 'admin_otp_enabled'");
    $stmt->execute();
    $otpEnabled = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$otpEnabled || $otpEnabled['setting_value'] !== 'true') {
        throw new Exception("OTP authentication is not enabled");
    }
    
    // Get OTP settings
    $stmt = $conn->query("SELECT setting_name, setting_value FROM admin_auth_settings WHERE setting_name IN ('admin_otp_expiry_time', 'admin_max_otp_attempts')");
    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_name']] = $row['setting_value'];
    }
    
    $otpExpiryTime = $settings['admin_otp_expiry_time'] ?? 300; // 5 minutes default
    $maxAttempts = $settings['admin_max_otp_attempts'] ?? 3;
    
    // Check for existing active OTP
    $stmt = $conn->prepare("SELECT id, attempts FROM admin_otp WHERE admin_id = ? AND expires_at > NOW() AND used = 0");
    $stmt->execute([$adminId]);
    $existingOtp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existingOtp && $existingOtp['attempts'] >= $maxAttempts) {
        throw new Exception("Maximum OTP attempts exceeded. Please wait before requesting a new OTP.");
    }
    
    // Check rate limiting (max 3 OTP requests per 15 minutes)
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM admin_otp WHERE admin_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)");
    $stmt->execute([$adminId]);
    $recentOtps = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($recentOtps >= 3) {
        throw new Exception("Too many OTP requests. Please wait 15 minutes before requesting another OTP.");
    }
    
    // Generate 6-digit OTP
    $otp = sprintf('%06d', random_int(100000, 999999));
    $expiresAt = date('Y-m-d H:i:s', time() + $otpExpiryTime);
    
    $conn->beginTransaction();
    
    // Invalidate any existing OTPs for this admin
    $stmt = $conn->prepare("UPDATE admin_otp SET used = 1 WHERE admin_id = ? AND used = 0");
    $stmt->execute([$adminId]);
    
    // Insert new OTP
    $stmt = $conn->prepare("INSERT INTO admin_otp (admin_id, otp, expires_at, attempts, used) VALUES (?, ?, ?, 0, 0)");
    $stmt->execute([$adminId, $otp, $expiresAt]);
    
    // Get SMTP settings
    $stmt = $conn->query("SELECT * FROM smtp_settings WHERE is_active = 1 LIMIT 1");
    $smtpSettings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$smtpSettings) {
        $conn->rollBack();
        throw new Exception("SMTP is not configured. Please configure email settings first.");
    }
    
    // Send OTP email
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtpSettings['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtpSettings['username'];
        $mail->Password = $smtpSettings['password'];
        $mail->SMTPSecure = $smtpSettings['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $smtpSettings['port'];
        
        // Recipients
        $mail->setFrom($smtpSettings['from_email'], $smtpSettings['from_name']);
        $mail->addAddress($admin['email'], $admin['username']);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'FanBet247 Admin Login - OTP Verification';
        
        $expiryMinutes = ceil($otpExpiryTime / 60);
        $mail->Body = "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #007bff;'>FanBet247 Admin Portal</h1>
                </div>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                    <h2 style='color: #333; margin-top: 0;'>Admin Login Verification</h2>
                    <p>Hello <strong>{$admin['username']}</strong>,</p>
                    <p>You have requested to log in to the FanBet247 Admin Portal. Please use the following One-Time Password (OTP) to complete your login:</p>
                    
                    <div style='text-align: center; margin: 30px 0;'>
                        <div style='background: #007bff; color: white; padding: 15px 30px; border-radius: 8px; font-size: 24px; font-weight: bold; letter-spacing: 3px; display: inline-block;'>
                            {$otp}
                        </div>
                    </div>
                    
                    <p><strong>Important:</strong></p>
                    <ul>
                        <li>This OTP is valid for <strong>{$expiryMinutes} minutes</strong></li>
                        <li>Do not share this code with anyone</li>
                        <li>If you did not request this login, please contact support immediately</li>
                    </ul>
                </div>
                
                <div style='border-top: 1px solid #ddd; padding-top: 20px; font-size: 12px; color: #666;'>
                    <p>This is an automated message from FanBet247 Admin Portal. Please do not reply to this email.</p>
                    <p>Login attempt from IP: {$_SERVER['REMOTE_ADDR']}</p>
                    <p>Time: " . date('Y-m-d H:i:s') . "</p>
                </div>
            </div>
        </body>
        </html>";
        
        $mail->AltBody = "FanBet247 Admin Login OTP: {$otp}\n\nThis OTP is valid for {$expiryMinutes} minutes.\nDo not share this code with anyone.\n\nLogin attempt from IP: {$_SERVER['REMOTE_ADDR']}\nTime: " . date('Y-m-d H:i:s');
        
        $mail->send();
        
        // Log the OTP send action
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, 'otp', 'otp_sent', ?, ?, ?)
        ");
        $stmt->execute([
            $adminId,
            json_encode(['expires_at' => $expiresAt, 'email' => $admin['email']]),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'OTP sent successfully to your registered email address',
            'expires_in' => $otpExpiryTime,
            'expires_at' => $expiresAt,
            'email_masked' => substr($admin['email'], 0, 2) . '***@' . substr($admin['email'], strpos($admin['email'], '@') + 1)
        ]);
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw new Exception("Failed to send OTP email: " . $e->getMessage());
    }
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
