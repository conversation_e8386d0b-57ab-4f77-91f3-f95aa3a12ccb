import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle, FaArrowLeft, FaRedo } from 'react-icons/fa';

const API_BASE_URL = '/backend';

const AdminOTPVerification = ({ adminId, username, onSuccess, onBack }) => {
    const [otp, setOtp] = useState(['', '', '', '', '', '']);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [timeLeft, setTimeLeft] = useState(0);
    const [canResend, setCanResend] = useState(false);
    const [resendLoading, setResendLoading] = useState(false);
    const [otpSent, setOtpSent] = useState(false);
    
    const inputRefs = useRef([]);

    useEffect(() => {
        // Send initial OTP when component mounts
        sendOTP();
    }, []);

    useEffect(() => {
        // Timer for OTP expiry
        if (timeLeft > 0) {
            const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
            return () => clearTimeout(timer);
        } else if (otpSent) {
            setCanResend(true);
        }
    }, [timeLeft, otpSent]);

    const sendOTP = async () => {
        try {
            setResendLoading(true);
            setError('');
            
            const response = await axios.post(`${API_BASE_URL}/handlers/admin_send_otp.php`, {
                admin_id: adminId
            });

            if (response.data.success) {
                setSuccess(`OTP sent to ${response.data.email_masked}`);
                setTimeLeft(response.data.expires_in || 300);
                setCanResend(false);
                setOtpSent(true);
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to send OTP');
            }
        } catch (err) {
            setError('Failed to send OTP. Please try again.');
            console.error('OTP send error:', err);
        } finally {
            setResendLoading(false);
        }
    };

    const handleOtpChange = (index, value) => {
        if (value.length > 1) return; // Prevent multiple characters
        
        const newOtp = [...otp];
        newOtp[index] = value;
        setOtp(newOtp);

        // Auto-focus next input
        if (value && index < 5) {
            inputRefs.current[index + 1]?.focus();
        }

        // Auto-submit when all fields are filled
        if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
            verifyOTP(newOtp.join(''));
        }
    };

    const handleKeyDown = (index, e) => {
        if (e.key === 'Backspace' && !otp[index] && index > 0) {
            inputRefs.current[index - 1]?.focus();
        }
    };

    const verifyOTP = async (otpCode = null) => {
        const codeToVerify = otpCode || otp.join('');
        
        if (codeToVerify.length !== 6) {
            setError('Please enter the complete 6-digit OTP code');
            return;
        }

        try {
            setLoading(true);
            setError('');

            const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_otp.php`, {
                admin_id: adminId,
                otp: codeToVerify
            });

            if (response.data.success) {
                setSuccess('OTP verified successfully!');
                setTimeout(() => {
                    onSuccess({
                        admin_id: response.data.admin_id,
                        username: response.data.username,
                        role: response.data.role,
                        session_token: response.data.session_token,
                        auth_method: response.data.auth_method
                    });
                }, 1000);
            } else {
                setError(response.data.message || 'Invalid OTP code');
                // Clear OTP inputs on error
                setOtp(['', '', '', '', '', '']);
                inputRefs.current[0]?.focus();
            }
        } catch (err) {
            setError('Failed to verify OTP. Please try again.');
            console.error('OTP verification error:', err);
            setOtp(['', '', '', '', '', '']);
            inputRefs.current[0]?.focus();
        } finally {
            setLoading(false);
        }
    };

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div>
                    <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                        <FaKey className="h-6 w-6 text-blue-600" />
                    </div>
                    <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        Enter OTP Code
                    </h2>
                    <p className="mt-2 text-center text-sm text-gray-600">
                        We've sent a 6-digit code to your registered email address
                    </p>
                    <p className="text-center text-sm font-medium text-gray-900">
                        {username}
                    </p>
                </div>

                <div className="mt-8 space-y-6">
                    {/* Error Message */}
                    {error && (
                        <div className="bg-red-50 border border-red-200 rounded-md p-4 flex items-center gap-3">
                            <FaExclamationTriangle className="text-red-500" />
                            <span className="text-red-700 text-sm">{error}</span>
                        </div>
                    )}

                    {/* Success Message */}
                    {success && (
                        <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-center gap-3">
                            <FaEnvelope className="text-green-500" />
                            <span className="text-green-700 text-sm">{success}</span>
                        </div>
                    )}

                    {/* OTP Input */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-4 text-center">
                            Enter the 6-digit code
                        </label>
                        <div className="flex justify-center space-x-3">
                            {otp.map((digit, index) => (
                                <input
                                    key={index}
                                    ref={el => inputRefs.current[index] = el}
                                    type="text"
                                    maxLength="1"
                                    value={digit}
                                    onChange={(e) => handleOtpChange(index, e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(index, e)}
                                    className="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    disabled={loading}
                                />
                            ))}
                        </div>
                    </div>

                    {/* Timer */}
                    {timeLeft > 0 && (
                        <div className="text-center">
                            <p className="text-sm text-gray-600">
                                Code expires in: <span className="font-medium text-blue-600">{formatTime(timeLeft)}</span>
                            </p>
                        </div>
                    )}

                    {/* Verify Button */}
                    <div>
                        <button
                            onClick={() => verifyOTP()}
                            disabled={loading || otp.join('').length !== 6}
                            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {loading && <FaSpinner className="animate-spin mr-2" />}
                            {loading ? 'Verifying...' : 'Verify OTP'}
                        </button>
                    </div>

                    {/* Resend OTP */}
                    <div className="text-center">
                        <button
                            onClick={sendOTP}
                            disabled={!canResend || resendLoading}
                            className="text-sm text-blue-600 hover:text-blue-500 disabled:text-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2 mx-auto"
                        >
                            {resendLoading ? (
                                <FaSpinner className="animate-spin" />
                            ) : (
                                <FaRedo />
                            )}
                            {resendLoading ? 'Sending...' : 'Resend OTP'}
                        </button>
                    </div>

                    {/* Back Button */}
                    <div className="text-center">
                        <button
                            onClick={onBack}
                            className="text-sm text-gray-600 hover:text-gray-500 flex items-center justify-center gap-2 mx-auto"
                        >
                            <FaArrowLeft />
                            Back to Login
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdminOTPVerification;
