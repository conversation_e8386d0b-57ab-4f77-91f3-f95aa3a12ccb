import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaShieldAlt, FaCheck, FaTimes, FaSave } from 'react-icons/fa';

const API_BASE_URL = '/backend';

function SecuritySettings() {
    const [settings, setSettings] = useState({
        enable_2fa: 'false',
        allowed_auth_methods: 'email_otp,google_auth',
        otp_expiry_time: '300',
        max_otp_attempts: '3',
        lockout_time: '1800',
        password_min_length: '8',
        require_special_chars: 'true',
        session_timeout: '3600',
        max_login_attempts: '5'
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    useEffect(() => {
        fetchSettings();
    }, []);

    const fetchSettings = async () => {
        try {
            setLoading(true);
            const response = await axios.get(`${API_BASE_URL}/handlers/get_security_settings.php`);

            if (response.data.success && response.data.settings) {
                const settingsData = {};
                Object.keys(response.data.settings).forEach(key => {
                    settingsData[key] = response.data.settings[key].value;
                });
                setSettings(settingsData);
            }
        } catch (err) {
            setError('Failed to load security settings');
            console.error('Error fetching settings:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setSettings(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (checked ? 'true' : 'false') : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setSuccess('');

        try {
            setSaving(true);
            const response = await axios.post(`${API_BASE_URL}/handlers/update_security_settings.php`, {
                settings: settings
            });

            if (response.data.success) {
                setSuccess('Security settings saved successfully!');
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to save security settings');
            }
        } catch (err) {
            setError('Failed to save security settings');
            console.error('Error saving settings:', err);
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <div className="p-6">
                <div className="flex items-center justify-center h-64">
                    <div className="text-lg text-gray-600">Loading security settings...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
                    <FaShieldAlt className="text-blue-500" />
                    Security Settings
                </h1>
                <p className="text-gray-600 mt-2">
                    Configure two-factor authentication (2FA) options and other security features.
                </p>
            </div>

            {/* Alerts */}
            {error && (
                <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                    <FaTimes className="text-red-500" />
                    <span className="text-red-700">{error}</span>
                </div>
            )}

            {success && (
                <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
                    <FaCheck className="text-green-500" />
                    <span className="text-green-700">{success}</span>
                </div>
            )}

            {/* Settings Form */}
            <div className="bg-white rounded-lg shadow-sm border">
                <form onSubmit={handleSubmit} className="p-6 space-y-6">
                    {/* Two-Factor Authentication */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Two-Factor Authentication</h2>
                        <div className="space-y-4">
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="enable_2fa"
                                    name="enable_2fa"
                                    checked={settings.enable_2fa === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="enable_2fa" className="ml-2 block text-sm text-gray-900">
                                    Enable Two-Factor Authentication for users
                                </label>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Allowed Authentication Methods
                                </label>
                                <input
                                    type="text"
                                    name="allowed_auth_methods"
                                    value={settings.allowed_auth_methods}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="email_otp,google_auth"
                                />
                                <p className="text-xs text-gray-500 mt-1">Comma-separated list of allowed methods</p>
                            </div>
                        </div>
                    </div>

                    {/* OTP Settings */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">OTP Settings</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    OTP Expiry Time (seconds)
                                </label>
                                <input
                                    type="number"
                                    name="otp_expiry_time"
                                    value={settings.otp_expiry_time}
                                    onChange={handleInputChange}
                                    min="60"
                                    max="3600"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Max OTP Attempts
                                </label>
                                <input
                                    type="number"
                                    name="max_otp_attempts"
                                    value={settings.max_otp_attempts}
                                    onChange={handleInputChange}
                                    min="1"
                                    max="10"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Password Security */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Password Security</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Minimum Password Length
                                </label>
                                <input
                                    type="number"
                                    name="password_min_length"
                                    value={settings.password_min_length}
                                    onChange={handleInputChange}
                                    min="6"
                                    max="50"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="require_special_chars"
                                    name="require_special_chars"
                                    checked={settings.require_special_chars === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="require_special_chars" className="ml-2 block text-sm text-gray-900">
                                    Require special characters in passwords
                                </label>
                            </div>
                        </div>
                    </div>

                    {/* Login Security */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Login Security</h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Max Login Attempts
                                </label>
                                <input
                                    type="number"
                                    name="max_login_attempts"
                                    value={settings.max_login_attempts}
                                    onChange={handleInputChange}
                                    min="1"
                                    max="20"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Lockout Time (seconds)
                                </label>
                                <input
                                    type="number"
                                    name="lockout_time"
                                    value={settings.lockout_time}
                                    onChange={handleInputChange}
                                    min="300"
                                    max="86400"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Session Timeout (seconds)
                                </label>
                                <input
                                    type="number"
                                    name="session_timeout"
                                    value={settings.session_timeout}
                                    onChange={handleInputChange}
                                    min="300"
                                    max="86400"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end pt-6 border-t">
                        <button
                            type="submit"
                            disabled={saving}
                            className="flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                            <FaSave />
                            {saving ? 'Saving...' : 'Save Security Settings'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}

export default SecuritySettings;
