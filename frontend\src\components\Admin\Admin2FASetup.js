import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaShieldAlt, FaQrcode, Fa<PERSON>ey, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaEx<PERSON><PERSON>riangle, FaArrowLeft, FaDownload } from 'react-icons/fa';

const API_BASE_URL = '/backend';

const Admin2FASetup = ({ adminId, username, onSuccess, onBack }) => {
    const [step, setStep] = useState(1); // 1: Setup, 2: Verify, 3: Backup Codes
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    
    // Setup data
    const [secretKey, setSecretKey] = useState('');
    const [qrCodeUrl, setQrCodeUrl] = useState('');
    const [backupCodes, setBackupCodes] = useState([]);
    const [manualEntry<PERSON><PERSON>, setManualEntryKey] = useState('');
    
    // Verification
    const [verificationCode, setVerificationCode] = useState('');
    const [verifying, setVerifying] = useState(false);
    
    // UI state
    const [copied, setCopied] = useState(false);
    const [backupCodesCopied, setBackupCodesCopied] = useState(false);

    useEffect(() => {
        initiate2FASetup();
    }, []);

    const initiate2FASetup = async () => {
        try {
            setLoading(true);
            setError('');

            const response = await axios.get(`${API_BASE_URL}/handlers/admin_setup_2fa.php?adminId=${adminId}`);

            if (response.data.success) {
                setSecretKey(response.data.secret_key);
                setQrCodeUrl(response.data.qr_code_url);
                setBackupCodes(response.data.backup_codes);
                setManualEntryKey(response.data.manual_entry_key);
                setSuccess('2FA setup initiated successfully');
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to initiate 2FA setup');
            }
        } catch (err) {
            setError('Failed to initiate 2FA setup. Please try again.');
            console.error('2FA setup error:', err);
        } finally {
            setLoading(false);
        }
    };

    const verify2FASetup = async () => {
        if (!verificationCode || verificationCode.length !== 6) {
            setError('Please enter a valid 6-digit code from your authenticator app');
            return;
        }

        try {
            setVerifying(true);
            setError('');

            const response = await axios.post(`${API_BASE_URL}/handlers/admin_setup_2fa.php?adminId=${adminId}`, {
                verification_code: verificationCode
            });

            if (response.data.success) {
                setSuccess('2FA setup completed successfully!');
                setStep(3); // Show backup codes
            } else {
                setError(response.data.message || 'Invalid verification code');
            }
        } catch (err) {
            setError('Failed to verify 2FA setup. Please try again.');
            console.error('2FA verification error:', err);
        } finally {
            setVerifying(false);
        }
    };

    const copyToClipboard = async (text, type = 'key') => {
        try {
            await navigator.clipboard.writeText(text);
            if (type === 'key') {
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            } else {
                setBackupCodesCopied(true);
                setTimeout(() => setBackupCodesCopied(false), 2000);
            }
        } catch (err) {
            console.error('Failed to copy to clipboard:', err);
        }
    };

    const downloadBackupCodes = () => {
        const content = `FanBet247 Admin 2FA Backup Codes\nGenerated: ${new Date().toLocaleString()}\nAdmin: ${username}\n\n${backupCodes.join('\n')}\n\nKeep these codes safe and secure. Each code can only be used once.`;
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `fanbet247-backup-codes-${username}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const complete2FASetup = () => {
        onSuccess({
            message: '2FA has been successfully enabled for your account',
            auth_method: '2fa',
            backup_codes: backupCodes
        });
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <FaSpinner className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
                    <p className="text-gray-600">Setting up 2FA...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div>
                    <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
                        <FaShieldAlt className="h-6 w-6 text-green-600" />
                    </div>
                    <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        Setup Two-Factor Authentication
                    </h2>
                    <p className="mt-2 text-center text-sm text-gray-600">
                        Secure your admin account with Google Authenticator
                    </p>
                    <p className="text-center text-sm font-medium text-gray-900">
                        {username}
                    </p>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4 flex items-center gap-3">
                        <FaExclamationTriangle className="text-red-500" />
                        <span className="text-red-700 text-sm">{error}</span>
                    </div>
                )}

                {/* Success Message */}
                {success && (
                    <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-center gap-3">
                        <FaCheck className="text-green-500" />
                        <span className="text-green-700 text-sm">{success}</span>
                    </div>
                )}

                {/* Step 1: QR Code Setup */}
                {step === 1 && (
                    <div className="space-y-6">
                        <div className="text-center">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Step 1: Scan QR Code</h3>
                            <p className="text-sm text-gray-600 mb-6">
                                Scan this QR code with your Google Authenticator app
                            </p>
                            
                            {qrCodeUrl && (
                                <div className="bg-white p-4 rounded-lg border inline-block">
                                    <img src={qrCodeUrl} alt="2FA QR Code" className="w-48 h-48 mx-auto" />
                                </div>
                            )}
                        </div>

                        <div>
                            <h4 className="text-md font-medium text-gray-900 mb-2">Manual Entry</h4>
                            <p className="text-sm text-gray-600 mb-3">
                                If you can't scan the QR code, enter this key manually:
                            </p>
                            <div className="flex items-center gap-2">
                                <input
                                    type="text"
                                    value={manualEntryKey}
                                    readOnly
                                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm font-mono"
                                />
                                <button
                                    onClick={() => copyToClipboard(manualEntryKey)}
                                    className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
                                >
                                    {copied ? <FaCheck /> : <FaCopy />}
                                    {copied ? 'Copied!' : 'Copy'}
                                </button>
                            </div>
                        </div>

                        <button
                            onClick={() => setStep(2)}
                            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            Next: Verify Setup
                        </button>
                    </div>
                )}

                {/* Step 2: Verification */}
                {step === 2 && (
                    <div className="space-y-6">
                        <div className="text-center">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Step 2: Verify Setup</h3>
                            <p className="text-sm text-gray-600 mb-6">
                                Enter the 6-digit code from your Google Authenticator app
                            </p>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Verification Code
                            </label>
                            <input
                                type="text"
                                maxLength="6"
                                value={verificationCode}
                                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center text-xl font-mono"
                                placeholder="000000"
                            />
                        </div>

                        <div className="flex gap-3">
                            <button
                                onClick={() => setStep(1)}
                                className="flex-1 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                                Back
                            </button>
                            <button
                                onClick={verify2FASetup}
                                disabled={verifying || verificationCode.length !== 6}
                                className="flex-1 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {verifying && <FaSpinner className="animate-spin mr-2" />}
                                {verifying ? 'Verifying...' : 'Verify & Enable 2FA'}
                            </button>
                        </div>
                    </div>
                )}

                {/* Step 3: Backup Codes */}
                {step === 3 && (
                    <div className="space-y-6">
                        <div className="text-center">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Step 3: Save Backup Codes</h3>
                            <p className="text-sm text-gray-600 mb-6">
                                Save these backup codes in a secure location. Each code can only be used once.
                            </p>
                        </div>

                        <div className="bg-gray-50 rounded-lg p-4">
                            <div className="grid grid-cols-2 gap-2 font-mono text-sm">
                                {backupCodes.map((code, index) => (
                                    <div key={index} className="bg-white p-2 rounded border text-center">
                                        {code}
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="flex gap-3">
                            <button
                                onClick={() => copyToClipboard(backupCodes.join('\n'), 'codes')}
                                className="flex-1 flex justify-center items-center gap-2 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                                {backupCodesCopied ? <FaCheck /> : <FaCopy />}
                                {backupCodesCopied ? 'Copied!' : 'Copy Codes'}
                            </button>
                            <button
                                onClick={downloadBackupCodes}
                                className="flex-1 flex justify-center items-center gap-2 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                                <FaDownload />
                                Download
                            </button>
                        </div>

                        <button
                            onClick={complete2FASetup}
                            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                            Complete Setup
                        </button>
                    </div>
                )}

                {/* Back to Login */}
                <div className="text-center">
                    <button
                        onClick={onBack}
                        className="text-sm text-gray-600 hover:text-gray-500 flex items-center justify-center gap-2 mx-auto"
                    >
                        <FaArrowLeft />
                        Back to Login
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Admin2FASetup;
