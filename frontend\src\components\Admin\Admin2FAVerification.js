import React, { useState } from 'react';
import axios from 'axios';
import { FaS<PERSON>eld<PERSON>lt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaEx<PERSON><PERSON><PERSON>gle, FaArrowLeft, FaQuestionCircle } from 'react-icons/fa';

const API_BASE_URL = '/backend';

const Admin2FAVerification = ({ adminId, username, onSuccess, onBack }) => {
    const [verificationCode, setVerificationCode] = useState('');
    const [backupCode, setBackupCode] = useState('');
    const [useBackupCode, setUseBackupCode] = useState(false);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    const verify2FA = async () => {
        const code = useBackupCode ? backupCode : verificationCode;
        
        if (!code || (useBackupCode ? code.length !== 8 : code.length !== 6)) {
            setError(useBackupCode ? 'Please enter a valid 8-character backup code' : 'Please enter a valid 6-digit code');
            return;
        }

        try {
            setLoading(true);
            setError('');

            const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_2fa.php`, {
                admin_id: adminId,
                code: code,
                is_backup_code: useBackupCode
            });

            if (response.data.success) {
                setSuccess('2FA verification successful!');
                
                // Show warning if backup codes are running low
                if (response.data.backup_code_warning) {
                    setTimeout(() => {
                        alert(response.data.backup_code_warning);
                    }, 1000);
                }
                
                setTimeout(() => {
                    onSuccess({
                        admin_id: response.data.admin_id,
                        username: response.data.username,
                        role: response.data.role,
                        session_token: response.data.session_token,
                        auth_method: response.data.auth_method,
                        used_backup_code: response.data.used_backup_code,
                        remaining_backup_codes: response.data.remaining_backup_codes
                    });
                }, 1000);
            } else {
                setError(response.data.message || 'Invalid verification code');
                // Clear the input on error
                if (useBackupCode) {
                    setBackupCode('');
                } else {
                    setVerificationCode('');
                }
            }
        } catch (err) {
            setError('Failed to verify 2FA code. Please try again.');
            console.error('2FA verification error:', err);
            if (useBackupCode) {
                setBackupCode('');
            } else {
                setVerificationCode('');
            }
        } finally {
            setLoading(false);
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            verify2FA();
        }
    };

    const toggleBackupCode = () => {
        setUseBackupCode(!useBackupCode);
        setVerificationCode('');
        setBackupCode('');
        setError('');
    };

    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div>
                    <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
                        <FaShieldAlt className="h-6 w-6 text-green-600" />
                    </div>
                    <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        Two-Factor Authentication
                    </h2>
                    <p className="mt-2 text-center text-sm text-gray-600">
                        {useBackupCode 
                            ? 'Enter one of your backup codes'
                            : 'Enter the code from your Google Authenticator app'
                        }
                    </p>
                    <p className="text-center text-sm font-medium text-gray-900">
                        {username}
                    </p>
                </div>

                <div className="mt-8 space-y-6">
                    {/* Error Message */}
                    {error && (
                        <div className="bg-red-50 border border-red-200 rounded-md p-4 flex items-center gap-3">
                            <FaExclamationTriangle className="text-red-500" />
                            <span className="text-red-700 text-sm">{error}</span>
                        </div>
                    )}

                    {/* Success Message */}
                    {success && (
                        <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-center gap-3">
                            <FaKey className="text-green-500" />
                            <span className="text-green-700 text-sm">{success}</span>
                        </div>
                    )}

                    {/* Verification Input */}
                    <div>
                        {useBackupCode ? (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Backup Code
                                </label>
                                <input
                                    type="text"
                                    maxLength="8"
                                    value={backupCode}
                                    onChange={(e) => setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, ''))}
                                    onKeyPress={handleKeyPress}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 text-center text-lg font-mono"
                                    placeholder="XXXXXXXX"
                                    disabled={loading}
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                    Enter one of your 8-character backup codes
                                </p>
                            </div>
                        ) : (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Authenticator Code
                                </label>
                                <input
                                    type="text"
                                    maxLength="6"
                                    value={verificationCode}
                                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                                    onKeyPress={handleKeyPress}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 text-center text-xl font-mono"
                                    placeholder="000000"
                                    disabled={loading}
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                    Enter the 6-digit code from Google Authenticator
                                </p>
                            </div>
                        )}
                    </div>

                    {/* Verify Button */}
                    <div>
                        <button
                            onClick={verify2FA}
                            disabled={loading || (useBackupCode ? backupCode.length !== 8 : verificationCode.length !== 6)}
                            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {loading && <FaSpinner className="animate-spin mr-2" />}
                            {loading ? 'Verifying...' : 'Verify Code'}
                        </button>
                    </div>

                    {/* Toggle Backup Code */}
                    <div className="text-center">
                        <button
                            onClick={toggleBackupCode}
                            className="text-sm text-blue-600 hover:text-blue-500 flex items-center justify-center gap-2 mx-auto"
                            disabled={loading}
                        >
                            <FaQuestionCircle />
                            {useBackupCode ? 'Use Authenticator App' : 'Use Backup Code'}
                        </button>
                    </div>

                    {/* Help Text */}
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div className="flex items-start gap-3">
                            <FaQuestionCircle className="text-blue-500 mt-0.5" />
                            <div className="text-sm text-blue-700">
                                <p className="font-medium mb-1">Need help?</p>
                                {useBackupCode ? (
                                    <p>
                                        Backup codes are 8-character codes you saved when setting up 2FA. 
                                        Each code can only be used once.
                                    </p>
                                ) : (
                                    <p>
                                        Open your Google Authenticator app and find the 6-digit code for FanBet247 Admin. 
                                        The code changes every 30 seconds.
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Back Button */}
                    <div className="text-center">
                        <button
                            onClick={onBack}
                            className="text-sm text-gray-600 hover:text-gray-500 flex items-center justify-center gap-2 mx-auto"
                            disabled={loading}
                        >
                            <FaArrowLeft />
                            Back to Login
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Admin2FAVerification;
