import React, { useState } from 'react';
import axios from 'axios';
import { Fa<PERSON><PERSON><PERSON><PERSON>lt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamation<PERSON>riangle, FaArrowLeft, FaQuestionCircle } from 'react-icons/fa';
import '../../pages/AdminLoginPage.css';

const API_BASE_URL = '/backend';

const Admin2FAVerification = ({ adminId, username, onSuccess, onBack }) => {
    const [verificationCode, setVerificationCode] = useState('');
    const [backupCode, setBackupCode] = useState('');
    const [useBackupCode, setUseBackupCode] = useState(false);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    const verify2FA = async () => {
        const code = useBackupCode ? backupCode : verificationCode;
        
        if (!code || (useBackupCode ? code.length !== 8 : code.length !== 6)) {
            setError(useBackupCode ? 'Please enter a valid 8-character backup code' : 'Please enter a valid 6-digit code');
            return;
        }

        try {
            setLoading(true);
            setError('');

            const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_2fa.php`, {
                admin_id: adminId,
                code: code,
                is_backup_code: useBackupCode
            });

            if (response.data.success) {
                setSuccess('2FA verification successful!');
                
                // Show warning if backup codes are running low
                if (response.data.backup_code_warning) {
                    setTimeout(() => {
                        alert(response.data.backup_code_warning);
                    }, 1000);
                }
                
                setTimeout(() => {
                    onSuccess({
                        admin_id: response.data.admin_id,
                        username: response.data.username,
                        role: response.data.role,
                        session_token: response.data.session_token,
                        auth_method: response.data.auth_method,
                        used_backup_code: response.data.used_backup_code,
                        remaining_backup_codes: response.data.remaining_backup_codes
                    });
                }, 1000);
            } else {
                setError(response.data.message || 'Invalid verification code');
                // Clear the input on error
                if (useBackupCode) {
                    setBackupCode('');
                } else {
                    setVerificationCode('');
                }
            }
        } catch (err) {
            setError('Failed to verify 2FA code. Please try again.');
            console.error('2FA verification error:', err);
            if (useBackupCode) {
                setBackupCode('');
            } else {
                setVerificationCode('');
            }
        } finally {
            setLoading(false);
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            verify2FA();
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        verify2FA();
    };

    const toggleBackupCode = () => {
        setUseBackupCode(!useBackupCode);
        setVerificationCode('');
        setBackupCode('');
        setError('');
    };

    return (
        <div className="admin-login-container">
            <div className="login-left-panel">
                <div className="login-logo">
                    <div className="logo-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                    </div>
                    <h1>FanBet247</h1>
                </div>

                <div className="login-form-container">
                    <h2>Two-Factor Authentication</h2>
                    <p className="login-subtitle">
                        {useBackupCode
                            ? 'Enter one of your backup codes to continue'
                            : 'Enter the 6-digit code from your Google Authenticator app'
                        }
                    </p>

                    {/* User Info */}
                    <div className="security-notice">
                        <div className="security-icon">
                            <FaShieldAlt />
                        </div>
                        <span>Logged in as: <strong>{username}</strong></span>
                    </div>

                    {/* Error Message */}
                    {error && <div className="error-message">{error}</div>}

                    {/* Success Message */}
                    {success && (
                        <div style={{
                            padding: '0.75rem',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            color: '#16a34a',
                            borderRadius: '0.5rem',
                            marginBottom: '1.5rem',
                            textAlign: 'center',
                            fontSize: '0.875rem',
                            border: '1px solid rgba(34, 197, 94, 0.2)'
                        }}>
                            {success}
                        </div>
                    )}

                    <form onSubmit={handleSubmit}>
                        {/* Verification Input */}
                        <div className="form-group">
                            {useBackupCode ? (
                                <div>
                                    <label htmlFor="backupCode">Backup Code</label>
                                    <div className="input-container">
                                        <input
                                            type="text"
                                            id="backupCode"
                                            maxLength="8"
                                            value={backupCode}
                                            onChange={(e) => setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, ''))}
                                            onKeyPress={handleKeyPress}
                                            placeholder="XXXXXXXX"
                                            disabled={loading}
                                            style={{
                                                textAlign: 'center',
                                                fontSize: '1.125rem',
                                                fontFamily: 'monospace',
                                                letterSpacing: '0.1em'
                                            }}
                                        />
                                        <div className="input-icon">
                                            <FaKey />
                                        </div>
                                    </div>
                                    <p style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                                        Enter one of your 8-character backup codes
                                    </p>
                                </div>
                            ) : (
                                <div>
                                    <label htmlFor="verificationCode">Authenticator Code</label>
                                    <div className="input-container">
                                        <input
                                            type="text"
                                            id="verificationCode"
                                            maxLength="6"
                                            value={verificationCode}
                                            onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                                            onKeyPress={handleKeyPress}
                                            placeholder="000000"
                                            disabled={loading}
                                            style={{
                                                textAlign: 'center',
                                                fontSize: '1.25rem',
                                                fontFamily: 'monospace',
                                                letterSpacing: '0.2em'
                                            }}
                                        />
                                        <div className="input-icon">
                                            <FaShieldAlt />
                                        </div>
                                    </div>
                                    <p style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                                        Enter the 6-digit code from Google Authenticator
                                    </p>
                                </div>
                            )}
                        </div>

                        {/* Form Options */}
                        <div style={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '0.75rem',
                            marginBottom: '1.5rem',
                            alignItems: 'center'
                        }}>
                            <button
                                type="button"
                                onClick={toggleBackupCode}
                                disabled={loading}
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '0.5rem',
                                    color: '#2C5F2D',
                                    fontSize: '0.875rem',
                                    textDecoration: 'none',
                                    transition: 'color 0.3s ease',
                                    background: 'none',
                                    border: 'none',
                                    cursor: 'pointer',
                                    padding: '0.5rem',
                                    borderRadius: '0.375rem',
                                    whiteSpace: 'nowrap'
                                }}
                                onMouseOver={(e) => e.target.style.color = '#224924'}
                                onMouseOut={(e) => e.target.style.color = '#2C5F2D'}
                            >
                                <FaQuestionCircle />
                                {useBackupCode ? 'Use Authenticator App' : 'Use Backup Code'}
                            </button>

                            <button
                                type="button"
                                onClick={onBack}
                                disabled={loading}
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    gap: '0.5rem',
                                    backgroundColor: '#dc2626',
                                    color: 'white',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    border: 'none',
                                    borderRadius: '0.5rem',
                                    padding: '0.75rem 1.5rem',
                                    cursor: 'pointer',
                                    transition: 'all 0.2s ease',
                                    whiteSpace: 'nowrap',
                                    boxShadow: '0 2px 4px rgba(220, 38, 38, 0.3)',
                                    transform: 'translateY(0)'
                                }}
                                onMouseOver={(e) => {
                                    e.target.style.backgroundColor = '#b91c1c';
                                    e.target.style.transform = 'translateY(-1px)';
                                    e.target.style.boxShadow = '0 4px 8px rgba(220, 38, 38, 0.4)';
                                }}
                                onMouseOut={(e) => {
                                    e.target.style.backgroundColor = '#dc2626';
                                    e.target.style.transform = 'translateY(0)';
                                    e.target.style.boxShadow = '0 2px 4px rgba(220, 38, 38, 0.3)';
                                }}
                            >
                                <FaArrowLeft />
                                Back to Login
                            </button>
                        </div>

                        {/* Verify Button */}
                        <button
                            type="submit"
                            className="login-button"
                            disabled={loading || (useBackupCode ? backupCode.length !== 8 : verificationCode.length !== 6)}
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '0.5rem'
                            }}
                        >
                            {loading && <FaSpinner className="animate-spin" />}
                            {loading ? 'Verifying...' : 'Verify Code'}
                        </button>
                    </form>

                    {/* Help Text */}
                    <div style={{
                        padding: '0.75rem 1rem',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        border: '1px solid rgba(59, 130, 246, 0.2)',
                        borderRadius: '0.5rem',
                        marginTop: '1.5rem'
                    }}>
                        <div style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem' }}>
                            <FaQuestionCircle style={{ color: '#3b82f6', marginTop: '0.125rem', flexShrink: 0 }} />
                            <div style={{ fontSize: '0.875rem', color: '#1e40af' }}>
                                <p style={{ fontWeight: '500', marginBottom: '0.25rem' }}>Need help?</p>
                                {useBackupCode ? (
                                    <p style={{ margin: 0 }}>
                                        Backup codes are 8-character codes you saved when setting up 2FA.
                                        Each code can only be used once.
                                    </p>
                                ) : (
                                    <p style={{ margin: 0 }}>
                                        Open your Google Authenticator app and find the 6-digit code for FanBet247 Admin.
                                        The code changes every 30 seconds.
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="login-right-panel"></div>
        </div>
    );
};

export default Admin2FAVerification;
